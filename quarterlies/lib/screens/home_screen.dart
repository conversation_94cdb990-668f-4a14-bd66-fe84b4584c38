import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/screens/customers/customer_list_screen.dart';
import 'package:quarterlies/screens/settings/settings_screen.dart';
import 'package:quarterlies/screens/tax_payments/tax_payment_list_screen.dart';
import 'package:quarterlies/screens/contracts/contract_list_screen.dart';
import 'package:quarterlies/screens/search/voice_search_screen.dart';
import 'package:quarterlies/screens/expenses/overhead_management_screen.dart';
import 'package:quarterlies/services/auth_service.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  Widget _buildDashboardCard(
    BuildContext context,
    DisplaySettingsProvider displayProvider, {
    required String title,
    required IconData icon,
    required String description,
    required VoidCallback onTap,
  }) {
    // Enhanced card with larger touch targets and better contrast
    return Card(
      elevation: displayProvider.isOfficeMode ? 2 : 4,
      margin: EdgeInsets.symmetric(
        vertical: displayProvider.isOfficeMode ? 6.0 : 8.0,
        horizontal: 4.0,
      ),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: EdgeInsets.all(displayProvider.isOfficeMode ? 16.0 : 20.0),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(
                  displayProvider.isOfficeMode ? 10.0 : 12.0,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: Icon(
                  icon,
                  size: displayProvider.isOfficeMode ? 36 : 48,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              SizedBox(width: displayProvider.isOfficeMode ? 16 : 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 16 : 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: displayProvider.isOfficeMode ? 4 : 6),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: displayProvider.isOfficeMode ? 13 : 16,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                size: displayProvider.isOfficeMode ? 24 : 32,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOfficeGridCards(BuildContext context) {
    final cardData = [
      {
        'title': 'Customers',
        'icon': Icons.people,
        'route':
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CustomerListScreen(),
              ),
            ),
      },
      {
        'title': 'Jobs',
        'icon': Icons.work,
        'route': () => Navigator.pushNamed(context, '/jobs'),
      },
      {
        'title': 'Invoices',
        'icon': Icons.receipt_long,
        'route': () => ErrorDisplay.showInfo(context, 'Coming soon!'),
      },
      {
        'title': 'Contracts',
        'icon': Icons.description,
        'route':
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ContractListScreen(),
              ),
            ),
      },
      {
        'title': 'Tax Payments',
        'icon': Icons.payments,
        'route':
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const TaxPaymentListScreen(),
              ),
            ),
      },
      {
        'title': 'Overhead',
        'icon': Icons.business_center,
        'route':
            () => Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OverheadManagementScreen(),
              ),
            ),
      },
    ];

    return Column(
      children: [
        for (int i = 0; i < cardData.length; i += 2)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 4.0),
            child: Row(
              children: [
                Expanded(
                  child: _buildCompactCard(
                    context,
                    cardData[i]['title'] as String,
                    cardData[i]['icon'] as IconData,
                    cardData[i]['route'] as VoidCallback,
                  ),
                ),
                const SizedBox(width: 8),
                if (i + 1 < cardData.length)
                  Expanded(
                    child: _buildCompactCard(
                      context,
                      cardData[i + 1]['title'] as String,
                      cardData[i + 1]['icon'] as IconData,
                      cardData[i + 1]['route'] as VoidCallback,
                    ),
                  )
                else
                  const Expanded(child: SizedBox()),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCompactCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final authService = AuthService();
    final user = authService.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: ResponsiveTitle(
          'Quarterlies',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        // App bar actions with larger touch targets
        actions: [
          // Voice search button
          IconButton(
            icon: Icon(
              Icons.mic,
              size: spacing.ResponsiveSpacing.getIconSize(context, base: 28),
            ),
            padding: spacing.ResponsiveSpacing.getPadding(context, base: 12),
            tooltip: 'Search with voice',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const VoiceSearchScreen(),
                ),
              );
            },
          ),
          // Settings button
          Padding(
            padding: EdgeInsets.only(
              right: spacing.ResponsiveSpacing.getSpacing(context, base: 8),
            ),
            child: IconButton(
              icon: Icon(
                Icons.settings,
                size: spacing.ResponsiveSpacing.getIconSize(context, base: 28),
              ),
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 12),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const SettingsScreen(),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      // Using SafeArea to ensure content is visible on all devices
      body: ResponsiveLayout(
        child: Consumer<DisplaySettingsProvider>(
          builder: (context, displayProvider, child) {
            return ListView(
              padding: spacing.ResponsiveSpacing.getPadding(context, base: 16),
              children: [
                // Welcome section with high contrast
                Container(
                  padding: spacing.ResponsiveSpacing.getPadding(
                    context,
                    base: 16,
                  ),
                  decoration: BoxDecoration(
                    color:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(
                      spacing.ResponsiveSpacing.getBorderRadius(
                        context,
                        base: 16,
                      ),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.05),
                        blurRadius: 10.0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ResponsiveSubtitle(
                        'Welcome, ${user?.email?.split('@').first ?? "User"}!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 8,
                        ),
                      ),
                      ResponsiveBody(
                        'Your business management hub - access all features from here.',
                        style: TextStyle(
                          color: Theme.of(
                            context,
                          ).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 24,
                  ),
                ),

                // Section header with improved visibility
                Container(
                  padding: spacing.ResponsiveSpacing.getPadding(
                    context,
                    base: 16,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primaryContainer.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(
                      spacing.ResponsiveSpacing.getBorderRadius(
                        context,
                        base: 12,
                      ),
                    ),
                  ),
                  child: ResponsiveSubtitle(
                    'Manage Your Business',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                  ),
                ),

                SizedBox(
                  height: spacing.ResponsiveSpacing.getSpacing(
                    context,
                    base: 16,
                  ),
                ),

                // Dashboard cards with improved spacing
                if (displayProvider.isOfficeMode) ...[
                  // Office Mode: Grid layout for more compact display
                  _buildOfficeGridCards(context),
                ] else ...[
                  // Field Mode: Existing card layout
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Customers',
                    icon: Icons.people,
                    description: 'Manage your customer database',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const CustomerListScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Jobs',
                    icon: Icons.work,
                    description: 'Manage your jobs and projects',
                    onTap: () {
                      Navigator.pushNamed(context, '/jobs');
                    },
                  ),
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Invoices & Payments',
                    icon: Icons.receipt_long,
                    description: 'Manage invoices and track payments',
                    onTap: () {
                      Navigator.pushNamed(context, '/invoices');
                    },
                  ),
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Contracts',
                    icon: Icons.description,
                    description: 'Manage contracts with customers',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ContractListScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Tax Payments',
                    icon: Icons.payments,
                    description: 'Track your estimated tax payments',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const TaxPaymentListScreen(),
                        ),
                      );
                    },
                  ),
                  _buildDashboardCard(
                    context,
                    displayProvider,
                    title: 'Overhead Expenses',
                    icon: Icons.business_center,
                    description: 'Manage business overhead expenses',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => const OverheadManagementScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }
}
