import 'package:flutter/material.dart';
import 'package:quarterlies/screens/customers/customer_form_screen.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/screens/estimates/estimate_form_screen.dart';
import 'package:quarterlies/screens/time_logs/time_log_form_screen.dart';
import 'package:quarterlies/screens/mileage/mileage_form_screen.dart';
import 'package:quarterlies/screens/jobs/job_form_screen.dart';
import 'package:quarterlies/screens/invoices/invoice_form_screen.dart';
import 'package:quarterlies/screens/contracts/contract_form_screen.dart';
import 'package:quarterlies/services/recent_data_service.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class BottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const BottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  /// Determine if labels should be shown based on screen size and orientation
  bool _shouldShowLabels(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Show labels on larger screens or portrait mode
    return screenWidth > 600 || !isLandscape;
  }

  /// Calculate appropriate text size to fit labels on one line
  double _calculateTextSize(
    BuildContext context,
    String label,
    bool showLabels,
  ) {
    if (!showLabels) return 0.0;

    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Base text size
    double baseSize = 12.0;

    // Adjust based on screen width and available space
    if (isLandscape && screenWidth < 800) {
      baseSize = 10.0; // Smaller text in landscape on smaller screens
    } else if (screenWidth > 600) {
      baseSize = 13.0; // Slightly larger on bigger screens
    }

    // Further adjust based on label length to ensure it fits
    if (label.length > 8) {
      baseSize *= 0.9;
    }

    return baseSize;
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<ConnectivityResult>>(
      stream: Connectivity().onConnectivityChanged,
      builder: (context, snapshot) {
        final isOffline =
            snapshot.data?.isEmpty ??
            false ||
                (snapshot.data?.length == 1 &&
                    snapshot.data?.first == ConnectivityResult.none);
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isOffline)
              Container(
                color: Colors.red,
                padding: const EdgeInsets.all(8.0),
                child: const Text(
                  'You are offline',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            Container(
              height: _getResponsiveBottomNavHeight(context),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51),
                    blurRadius: 8.0,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: ClipPath(
                clipper: _BottomNavClipper(),
                child: Container(
                  color: Theme.of(context).colorScheme.surface,
                  child: SafeArea(
                    top: false,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildNavItem(
                            context,
                            0,
                            Icons.dashboard,
                            'Dashboard',
                          ),
                          _buildNavItem(context, 1, Icons.home, 'Home'),
                          _buildNavItem(context, 2, Icons.work, 'Jobs'),
                          const SizedBox(width: 40), // Space for FAB
                          _buildNavItem(
                            context,
                            3,
                            Icons.receipt_long,
                            'Invoices',
                          ),
                          _buildNavItem(context, 4, Icons.bar_chart, 'Reports'),
                          _buildNavItem(context, 5, Icons.draw, 'Signatures'),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Get responsive bottom navigation height based on screen size and orientation
  double _getResponsiveBottomNavHeight(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;
    final isTablet = screenSize.shortestSide >= 600;

    if (isLandscape) {
      return isTablet ? 70.0 : 60.0;
    } else {
      return isTablet ? 80.0 : 75.0;
    }
  }

  Widget _buildNavItem(
    BuildContext context,
    int index,
    IconData icon,
    String label,
  ) {
    final isSelected = currentIndex == index;
    final screenWidth = MediaQuery.of(context).size.width;
    final isLandscape =
        MediaQuery.of(context).orientation == Orientation.landscape;

    // Calculate dynamic icon and text sizes based on screen width and orientation
    final iconSize =
        isLandscape
            ? (screenWidth > 800 ? 22.0 : 20.0)
            : (screenWidth > 400 ? 24.0 : 22.0);

    // Determine if we should show labels based on screen size and orientation
    final showLabels = _shouldShowLabels(context);

    // Calculate dynamic text size to fit on one line
    final textSize = _calculateTextSize(context, label, showLabels);

    return Expanded(
      child: InkWell(
        onTap: () {
          // Simplified navigation - remove authentication check since we're already in the main navigation
          // The user must be authenticated to reach this screen
          onTap(index);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 1.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon with tooltip for accessibility when labels are hidden
              showLabels
                  ? Icon(
                    icon,
                    size: iconSize,
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(
                              context,
                            ).colorScheme.onSurface.withAlpha(179),
                  )
                  : Tooltip(
                    message: label,
                    child: Icon(
                      icon,
                      size: iconSize,
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(
                                context,
                              ).colorScheme.onSurface.withAlpha(179),
                    ),
                  ),
              // Show label only if there's space
              if (showLabels) ...[
                const SizedBox(height: 2.0),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    label,
                    style: TextStyle(
                      fontSize: textSize,
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(
                                context,
                              ).colorScheme.onSurface.withAlpha(179),
                      fontWeight:
                          isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class QuickAddButton extends StatelessWidget {
  final int? currentScreenIndex;

  const QuickAddButton({super.key, this.currentScreenIndex});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 50.0, // Smaller than default FAB
      height: 50.0,
      child: FloatingActionButton(
        backgroundColor: const Color(0xFFFF6700), // Accent color #ff6700
        elevation: 6.0, // Reduced elevation for better embedding
        shape: const CircleBorder(),
        child: const Icon(Icons.add, size: 28, color: Colors.white),
        onPressed: () {
          _showQuickAddMenu(context);
        },
      ),
    );
  }

  void _showQuickAddMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => QuickAddMenu(currentScreenIndex: currentScreenIndex),
    );
  }
}

class QuickAddMenu extends StatefulWidget {
  final int? currentScreenIndex;

  const QuickAddMenu({super.key, this.currentScreenIndex});

  @override
  State<QuickAddMenu> createState() => _QuickAddMenuState();
}

class _QuickAddMenuState extends State<QuickAddMenu> {
  final RecentDataService _recentDataService = RecentDataService();
  List<Map<String, dynamic>> _recentJobs = [];
  List<Map<String, dynamic>> _recentCustomers = [];

  @override
  void initState() {
    super.initState();
    _fetchRecentData();
  }

  /// Get contextual title based on current screen
  String _getContextualTitle() {
    switch (widget.currentScreenIndex) {
      case 2: // Jobs screen
        return 'Add to Job';
      case 3: // Invoices screen
        return 'Create Invoice';
      default:
        return 'Quick Add';
    }
  }

  /// Build contextual options based on current screen
  List<Widget> _buildContextualOptions(BuildContext context) {
    switch (widget.currentScreenIndex) {
      case 2: // Jobs screen - show job-related options
        return [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.work,
                'Job',
                () => _navigateToJobForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.timer,
                'Time Log',
                () => _navigateToTimeLogForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.receipt,
                'Expense',
                () => _navigateToExpenseForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.directions_car,
                'Mileage',
                () => _navigateToMileageForm(context),
              ),
            ],
          ),
        ];
      case 3: // Invoices screen - show invoice-related options
        return [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.receipt_long,
                'Invoice',
                () => _navigateToInvoiceForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.description,
                'Estimate',
                () => _navigateToEstimateForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.assignment,
                'Contract',
                () => _navigateToContractForm(context),
              ),
            ],
          ),
        ];
      default: // All other screens - show general options
        return [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.receipt,
                'Expense',
                () => _navigateToExpenseForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.person_add,
                'Customer',
                () => _navigateToCustomerForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.description,
                'Estimate',
                () => _navigateToEstimateForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.timer,
                'Time Log',
                () => _navigateToTimeLogForm(context),
              ),
              _buildQuickAddOption(
                context,
                Icons.directions_car,
                'Mileage',
                () => _navigateToMileageForm(context),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickAddOption(
                context,
                Icons.business_center,
                'Overhead',
                () => _navigateToOverheadExpenseForm(context),
              ),
            ],
          ),
        ];
    }
  }

  Future<void> _fetchRecentData() async {
    try {
      final jobs = await _recentDataService.getRecentJobs();
      final customers = await _recentDataService.getRecentCustomers();

      if (!mounted) return; // Add mounted check here

      setState(() {
        _recentJobs = jobs;
        _recentCustomers = customers;
      });
    } catch (e) {
      if (!mounted) return; // Add mounted check here
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 24.0, horizontal: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getContextualTitle(),
            style: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 24),
          ..._buildContextualOptions(context),
          const SizedBox(height: 24),

          // Recent jobs section
          if (_recentJobs.isNotEmpty) ...[
            const Text(
              'Recent Jobs',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentJobs.length,
                itemBuilder: (context, index) {
                  final job = _recentJobs[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      onTap: () async {
                        // Update job usage count
                        _recentDataService.updateJobUsage(job['id']);

                        // Get default values based on recent entries
                        final defaults =
                            await _recentDataService.getTimeLogDefaults();

                        if (!mounted) return;

                        // Store context in local variable to ensure it's valid
                        final currentContext = context;
                        if (!mounted) return;
                        if (!currentContext.mounted) return;

                        Navigator.pop(currentContext); // Close the bottom sheet
                        Navigator.push(
                          currentContext,
                          MaterialPageRoute(
                            builder:
                                (context) => TimeLogFormScreen(
                                  initialJobId: job['id'],
                                  initialDefaults: defaults,
                                ),
                          ),
                        );
                      },
                      child: Container(
                        width: 120,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(
                                context,
                              ).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              job['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (job['customer'] != null)
                              Text(
                                job['customer']['name'],
                                style: TextStyle(
                                  fontSize: 12,
                                  color:
                                      Theme.of(
                                        context,
                                      ).colorScheme.onSurfaceVariant,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Recent customers section
          if (_recentCustomers.isNotEmpty) ...[
            const Text(
              'Recent Customers',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 80,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _recentCustomers[index];
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      onTap: () async {
                        // Update customer usage count
                        _recentDataService.updateCustomerUsage(customer['id']);

                        // Get default values based on recent entries
                        final defaults =
                            await _recentDataService.getEstimateDefaults();

                        if (!mounted) return;

                        // Store context in local variable to ensure it's valid
                        final currentContext = context;
                        if (!mounted) return;
                        if (!currentContext.mounted) return;

                        Navigator.pop(currentContext); // Close the bottom sheet
                        Navigator.push(
                          currentContext,
                          MaterialPageRoute(
                            builder:
                                (context) => EstimateFormScreen(
                                  initialCustomerId: customer['id'],
                                  initialDefaults: defaults,
                                ),
                          ),
                        );
                      },
                      child: Container(
                        width: 120,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color:
                              Theme.of(
                                context,
                              ).colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              customer['name'],
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              customer['email'] ?? '',
                              style: TextStyle(
                                fontSize: 12,
                                color:
                                    Theme.of(
                                      context,
                                    ).colorScheme.onSurfaceVariant,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAddOption(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 80,
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFFF6700).withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: const Color(0xFFFF6700), size: 28),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToExpenseForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getExpenseDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        if (!mounted) return; // Add mounted check before navigation
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => ExpenseFormScreen(
                  initialJobId: selectedJob['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => ExpenseFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToCustomerForm(BuildContext context) {
    Navigator.pop(context); // Close the bottom sheet
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomerFormScreen()),
    );
  }

  void _navigateToEstimateForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getEstimateDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent customers, show customer selection dialog
    if (_recentCustomers.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedCustomer = await _showCustomerSelectionDialog(
        currentContext,
      );
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedCustomer != null) {
        // Update customer usage count
        _recentDataService.updateCustomerUsage(selectedCustomer['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => EstimateFormScreen(
                  initialCustomerId: selectedCustomer['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no customer selected or no recent customers, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => EstimateFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToTimeLogForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getTimeLogDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        // Update job usage count
        _recentDataService.updateJobUsage(selectedJob['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder:
                (context) => TimeLogFormScreen(
                  initialJobId: selectedJob['id'],
                  initialDefaults: defaults,
                ),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder: (context) => TimeLogFormScreen(initialDefaults: defaults),
      ),
    );
  }

  void _navigateToMileageForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    if (!mounted) return;
    if (!currentContext.mounted) return;

    // If we have recent jobs, show job selection dialog
    if (_recentJobs.isNotEmpty) {
      if (!mounted) return; // Add mounted check before showing dialog
      if (!currentContext.mounted) return;

      final selectedJob = await _showJobSelectionDialog(currentContext);
      if (!mounted) return; // Existing mounted check after dialog
      if (!currentContext.mounted) return;

      if (selectedJob != null) {
        // Update job usage count
        _recentDataService.updateJobUsage(selectedJob['id']);

        if (!mounted) return;
        if (!currentContext.mounted) return;

        Navigator.push(
          currentContext,
          MaterialPageRoute(
            builder: (context) => MileageFormScreen(jobId: selectedJob['id']),
          ),
        );
        return;
      }
    }

    // If no job selected or no recent jobs, just navigate to form
    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(builder: (context) => const MileageFormScreen()),
    );
  }

  void _navigateToOverheadExpenseForm(BuildContext context) async {
    // Store context in local variable
    final currentContext = context;
    Navigator.pop(currentContext); // Close the bottom sheet

    // Get default values based on recent entries
    final defaults = await _recentDataService.getExpenseDefaults();

    if (!mounted) return;
    if (!currentContext.mounted) return;

    Navigator.push(
      currentContext,
      MaterialPageRoute(
        builder:
            (context) => ExpenseFormScreen(
              initialDefaults: defaults,
              isOverheadExpense: true, // Force overhead expense creation
            ),
      ),
    );
  }

  void _navigateToJobForm(BuildContext context) {
    Navigator.pop(context); // Close the bottom sheet
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const JobFormScreen()),
    );
  }

  void _navigateToInvoiceForm(BuildContext context) {
    Navigator.pop(context); // Close the bottom sheet
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InvoiceFormScreen()),
    );
  }

  void _navigateToContractForm(BuildContext context) {
    Navigator.pop(context); // Close the bottom sheet
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ContractFormScreen()),
    );
  }

  Future<Map<String, dynamic>?> _showJobSelectionDialog(
    BuildContext parentContext, // Renamed parameter
  ) async {
    return showDialog<Map<String, dynamic>>(
      context: parentContext,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Select Job'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _recentJobs.length,
                itemBuilder: (context, index) {
                  final job = _recentJobs[index];
                  return ListTile(
                    title: Text(job['name']),
                    subtitle: Text(job['customer']?['name'] ?? ''),
                    onTap: () {
                      if (!parentContext.mounted) {
                        return; // Check parent context
                      }
                      Navigator.of(dialogContext).pop(job);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop(null);
                },
                child: const Text('No Job'),
              ),
            ],
          ),
    );
  }

  Future<Map<String, dynamic>?> _showCustomerSelectionDialog(
    BuildContext parentContext, // Renamed parameter
  ) async {
    return showDialog<Map<String, dynamic>>(
      context: parentContext,
      builder:
          (dialogContext) => AlertDialog(
            title: const Text('Select Customer'),
            content: SizedBox(
              width: double.maxFinite,
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _recentCustomers.length,
                itemBuilder: (context, index) {
                  final customer = _recentCustomers[index];
                  return ListTile(
                    title: Text(customer['name']),
                    subtitle: Text(customer['email'] ?? ''),
                    onTap: () {
                      if (!parentContext.mounted) {
                        return; // Check parent context
                      }
                      // Update customer usage count
                      _recentDataService.updateCustomerUsage(customer['id']);
                      Navigator.of(dialogContext).pop(customer);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop();
                },
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  if (!parentContext.mounted) return; // Check parent context
                  Navigator.of(dialogContext).pop(null);
                },
                child: const Text('New Customer'),
              ),
            ],
          ),
    );
  }
}

/// Custom clipper to create a notch for the floating action button
class _BottomNavClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();

    // Start from top-left
    path.lineTo(0, 0);

    // Go to the start of the notch
    path.lineTo(size.width * 0.35, 0);

    // Create a circular notch for the FAB
    path.quadraticBezierTo(size.width * 0.40, 0, size.width * 0.40, 10);
    path.arcToPoint(
      Offset(size.width * 0.60, 10),
      radius: const Radius.circular(20),
      clockwise: false,
    );
    path.quadraticBezierTo(size.width * 0.60, 0, size.width * 0.65, 0);

    // Continue to top-right
    path.lineTo(size.width, 0);

    // Complete the rectangle
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
