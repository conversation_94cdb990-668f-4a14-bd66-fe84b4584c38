import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

/// A responsive AppBar that adapts to screen size, orientation, and display mode
/// Provides consistent styling and proper back button functionality across the app
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;
  final VoidCallback? onBackPressed;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = false,
    this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<DisplaySettingsProvider>(
      builder: (context, displayProvider, child) {
        return AppBar(
          title: ResponsiveTitle(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          backgroundColor:
              backgroundColor ?? Theme.of(context).colorScheme.primary,
          foregroundColor: foregroundColor ?? Colors.white,
          elevation:
              elevation ?? spacing.ResponsiveSpacing.getElevation(context),
          toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
          centerTitle: centerTitle,
          automaticallyImplyLeading: automaticallyImplyLeading,
          leading: leading ?? _buildResponsiveLeading(context),
          actions:
              actions
                  ?.map(
                    (action) =>
                        _wrapActionWithResponsivePadding(context, action),
                  )
                  .toList(),
        );
      },
    );
  }

  /// Build a responsive leading widget (back button) if needed
  Widget? _buildResponsiveLeading(BuildContext context) {
    if (!automaticallyImplyLeading) return null;

    // Check if we can pop the current route
    if (!Navigator.of(context).canPop()) return null;

    return IconButton(
      icon: Icon(
        Icons.arrow_back,
        size: spacing.ResponsiveSpacing.getIconSize(context),
      ),
      onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      tooltip: 'Back',
      padding: spacing.ResponsiveSpacing.getPadding(context, base: 8),
    );
  }

  /// Wrap action widgets with responsive padding for better touch targets
  Widget _wrapActionWithResponsivePadding(BuildContext context, Widget action) {
    if (action is IconButton) {
      return Padding(
        padding: spacing.ResponsiveSpacing.getPadding(context, base: 4),
        child: action,
      );
    }
    return action;
  }

  @override
  Size get preferredSize => const Size.fromHeight(64.0); // Default height, actual height set in build method
}

/// Extension to provide a convenient way to create responsive app bars
extension ResponsiveAppBarExtension on BuildContext {
  ResponsiveAppBar responsiveAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool automaticallyImplyLeading = true,
    Color? backgroundColor,
    Color? foregroundColor,
    double? elevation,
    bool centerTitle = false,
    VoidCallback? onBackPressed,
  }) {
    return ResponsiveAppBar(
      title: title,
      actions: actions,
      leading: leading,
      automaticallyImplyLeading: automaticallyImplyLeading,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      onBackPressed: onBackPressed,
    );
  }
}
