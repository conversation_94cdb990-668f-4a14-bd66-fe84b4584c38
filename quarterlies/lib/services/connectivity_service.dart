import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  // Singleton pattern
  static final ConnectivityService _instance = ConnectivityService._internal();
  factory ConnectivityService() => _instance;
  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final _connectionStatusController = StreamController<bool>.broadcast();

  // Track connection state for intelligent messaging
  bool _wasOffline = false;
  DateTime? _lastDisconnectTime;
  static const Duration _minimumOfflineTime = Duration(seconds: 10);

  // Stream that broadcasts connection status changes
  Stream<bool> get connectionStatus => _connectionStatusController.stream;

  // Initialize the service and start listening for connectivity changes
  void initialize() {
    _connectivity.onConnectivityChanged.listen((result) {
      // Handle the List<ConnectivityResult> by checking if any result indicates connectivity
      if (result.isNotEmpty) {
        _updateConnectionStatus(result.first);
      } else {
        _connectionStatusController.add(false);
      }
    });
    // Check initial connection state
    checkConnection();
  }

  // Check current connection status
  Future<bool> checkConnection() async {
    bool isConnected = false;
    try {
      final results = await _connectivity.checkConnectivity();
      // Handle the List<ConnectivityResult> by checking if any result indicates connectivity
      if (results.isNotEmpty) {
        isConnected = _isConnected(results.first);
      }
      _connectionStatusController.add(isConnected);
    } catch (e) {
      _connectionStatusController.add(false);
    }
    return isConnected;
  }

  // Update connection status based on connectivity result
  void _updateConnectionStatus(ConnectivityResult result) {
    final isConnected = _isConnected(result);

    // Track offline periods for intelligent messaging
    if (!isConnected && !_wasOffline) {
      // Just went offline
      _wasOffline = true;
      _lastDisconnectTime = DateTime.now();
    } else if (isConnected && _wasOffline) {
      // Just came back online
      _wasOffline = false;
      _lastDisconnectTime = null;
    }

    _connectionStatusController.add(isConnected);
  }

  // Helper method to determine if there is an active connection
  bool _isConnected(ConnectivityResult result) {
    return result == ConnectivityResult.wifi ||
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.ethernet;
  }

  /// Check if we should show the "back online" sync message
  /// Only show if the user was offline for a meaningful period
  bool shouldShowBackOnlineMessage() {
    if (_lastDisconnectTime == null) return false;

    final offlineDuration = DateTime.now().difference(_lastDisconnectTime!);
    return offlineDuration >= _minimumOfflineTime;
  }

  /// Reset the offline tracking (useful when app starts)
  void resetOfflineTracking() {
    _wasOffline = false;
    _lastDisconnectTime = null;
  }

  // Dispose resources
  void dispose() {
    _connectionStatusController.close();
  }
}
