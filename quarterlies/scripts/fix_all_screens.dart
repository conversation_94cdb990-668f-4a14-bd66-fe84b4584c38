#!/usr/bin/env dart

import 'dart:io';

/// <PERSON><PERSON><PERSON> to systematically fix all 62 screens for responsive design issues
/// This script identifies and reports issues that need manual fixing

void main() async {
  // ignore: avoid_print
  print('🔍 Starting comprehensive screen audit...\n');

  final screenDirectories = ['lib/screens'];

  final issues = <String, List<String>>{};

  for (final dir in screenDirectories) {
    await auditDirectory(dir, issues);
  }

  // ignore: avoid_print
  print('\n📊 AUDIT SUMMARY');
  // ignore: avoid_print
  print('=' * 50);

  var totalScreens = 0;
  var totalIssues = 0;

  for (final entry in issues.entries) {
    final file = entry.key;
    final fileIssues = entry.value;

    if (fileIssues.isNotEmpty) {
      totalScreens++;
      totalIssues += fileIssues.length;

      // ignore: avoid_print
      print('\n❌ $file');
      for (final issue in fileIssues) {
        // ignore: avoid_print
        print('   • $issue');
      }
    }
  }

  // ignore: avoid_print
  print('\n📈 STATISTICS');
  // ignore: avoid_print
  print('Total screens with issues: $totalScreens');
  // ignore: avoid_print
  print('Total issues found: $totalIssues');

  // ignore: avoid_print
  print('\n🔧 RECOMMENDED FIXES');
  // ignore: avoid_print
  print('1. Replace all fixed fontSize with ResponsiveText widgets');
  // ignore: avoid_print
  print('2. Replace TextOverflow.ellipsis with proper responsive sizing');
  // ignore: avoid_print
  print('3. Wrap all screen bodies with ResponsiveLayout');
  // ignore: avoid_print
  print('4. Ensure proper SafeArea usage');
  // ignore: avoid_print
  print('5. Add proper scrolling for all content');
}

Future<void> auditDirectory(
  String dirPath,
  Map<String, List<String>> issues,
) async {
  final dir = Directory(dirPath);

  if (!await dir.exists()) {
    // ignore: avoid_print
    print('⚠️  Directory not found: $dirPath');
    return;
  }

  await for (final entity in dir.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      await auditFile(entity, issues);
    }
  }
}

Future<void> auditFile(File file, Map<String, List<String>> issues) async {
  final content = await file.readAsString();
  final lines = content.split('\n');
  final fileName = file.path.replaceAll('quarterlies/', '');

  final fileIssues = <String>[];

  // Check for fixed font sizes
  for (int i = 0; i < lines.length; i++) {
    final line = lines[i];

    // Check for fixed fontSize
    if (line.contains('fontSize:') && line.contains(RegExp(r'\d+'))) {
      fileIssues.add(
        'Line ${i + 1}: Fixed fontSize found - needs ResponsiveText',
      );
    }

    // Check for TextOverflow.ellipsis
    if (line.contains('TextOverflow.ellipsis')) {
      fileIssues.add(
        'Line ${i + 1}: TextOverflow.ellipsis found - needs responsive solution',
      );
    }

    // Check for missing SafeArea in Scaffold body
    if (line.contains('body:') && i < lines.length - 5) {
      final nextFewLines = lines.skip(i).take(5).join(' ');
      if (!nextFewLines.contains('SafeArea') &&
          !nextFewLines.contains('ResponsiveLayout')) {
        fileIssues.add(
          'Line ${i + 1}: Scaffold body may need SafeArea or ResponsiveLayout',
        );
      }
    }

    // Check for hardcoded padding/margin values
    if (line.contains(RegExp(r'EdgeInsets\.(all|symmetric|only)\(\s*\d+'))) {
      fileIssues.add(
        'Line ${i + 1}: Hardcoded EdgeInsets - should be responsive',
      );
    }

    // Check for hardcoded sizes
    if (line.contains(RegExp(r'size:\s*\d+')) && !line.contains('Icons.')) {
      fileIssues.add('Line ${i + 1}: Hardcoded size - should be responsive');
    }
  }

  // Check for missing imports
  if (!content.contains('responsive_layout.dart') &&
      !content.contains('responsive_text.dart') &&
      content.contains('class ') &&
      content.contains('Screen') &&
      content.contains('Widget build')) {
    fileIssues.add(
      'Missing responsive imports - needs ResponsiveLayout and ResponsiveText',
    );
  }

  if (fileIssues.isNotEmpty) {
    issues[fileName] = fileIssues;
  }
}
